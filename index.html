<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Binary Options Trading Bot</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        :root {
            --primary-color: #4286f4;
            --secondary-color: #ff6384;
            --success-color: #10b981;
            --danger-color: #ef4444;
            --warning-color: #f59e0b;
            --dark-bg: #0f0f1a;
            --medium-bg: #1a1a2e;
            --light-bg: #28293d;
            --text-light: #ffffff;
            --text-medium: #d1d5db;
            --text-dark: #9ca3af;
            --border-color: rgba(66, 134, 244, 0.2);
        }

        body {
            font-family: 'Inter', '<PERSON><PERSON><PERSON>', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 0;
            background: linear-gradient(135deg, #0f0f1a 0%, #1a1a2e 100%);
            color: var(--text-light);
            min-height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .container {
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            background: rgba(28, 28, 40, 0.95);
            border-radius: 20px;
            box-shadow: 0 0 40px rgba(66, 134, 244, 0.15),
                        inset 0 0 20px rgba(66, 134, 244, 0.05);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(66, 134, 244, 0.1);
        }

        header {
            text-align: center;
            margin-bottom: 20px;
        }

        h1 {
            margin: 0;
            font-size: 24px;
            display: flex;
            align-items: center;
            justify-content: center;
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            color: transparent;
        }

        h1 i {
            margin-right: 10px;
            color: var(--primary-color);
        }

        .trading-modes {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
        }

        .mode-btn {
            flex: 1;
            padding: 15px;
            border: 2px solid var(--border-color);
            background-color: var(--medium-bg);
            color: var(--text-light);
            border-radius: 5px;
            cursor: pointer;
            text-align: center;
            transition: all 0.3s;
            position: relative;
            overflow: hidden;
        }

        .mode-btn.active {
            border-color: var(--secondary-color);
            background-color: var(--primary-color);
        }

        .mode-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
        }

        .mode-btn h3 {
            margin: 0 0 10px 0;
            color: var(--secondary-color);
        }

        .mode-btn p {
            margin: 0;
            font-size: 0.9em;
            color: var(--text-medium);
        }

        .mode-btn .mode-icon {
            position: absolute;
            top: 10px;
            right: 10px;
            font-size: 24px;
            opacity: 0.2;
        }

        .control-panel {
            background-color: var(--medium-bg);
            border-radius: 5px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            border: 1px solid var(--border-color);
        }

        .balance-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 1px solid var(--border-color);
        }

        .balance {
            font-size: 28px;
            font-weight: bold;
            color: var(--secondary-color);
        }

        .balance-change {
            font-size: 16px;
            padding: 5px 10px;
            border-radius: 15px;
            background-color: var(--success-color);
        }

        .balance-change.negative {
            background-color: var(--danger-color);
        }

        .settings {
            display: flex;
            flex-wrap: wrap;
            gap: 20px;
            margin-bottom: 20px;
        }

        .setting-group {
            flex: 1;
            min-width: 200px;
            background-color: var(--light-bg);
            padding: 15px;
            border-radius: 5px;
            border: 1px solid var(--border-color);
        }

        .setting-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: bold;
            color: var(--text-medium);
        }

        .setting-group select, .setting-group input[type="number"] {
            width: 100%;
            padding: 8px;
            border-radius: 4px;
            border: 1px solid var(--border-color);
            background-color: var(--medium-bg);
            color: var(--text-light);
        }

        .setting-group input[type="range"] {
            width: 100%;
            margin: 10px 0;
        }

        .buttons {
            display: flex;
            gap: 15px;
            margin-top: 20px;
        }

        button {
            padding: 12px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-weight: bold;
            transition: all 0.3s;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        button i {
            margin-right: 8px;
        }

        .primary-btn {
            background-color: var(--secondary-color);
            color: white;
            flex: 1;
        }

        .primary-btn:hover {
            background-color: #0284c7;
            transform: translateY(-2px);
        }

        .secondary-btn {
            background-color: var(--success-color);
            color: white;
            flex: 1;
        }

        .secondary-btn:hover {
            background-color: #059669;
            transform: translateY(-2px);
        }

        .content {
            display: grid;
            grid-template-columns: 2fr 1fr;
            gap: 20px;
        }

        .chart-container {
            background-color: var(--medium-bg);
            border-radius: 5px;
            padding: 20px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            border: 1px solid var(--border-color);
        }

        .chart-container h3 {
            margin-top: 0;
            color: var(--secondary-color);
            border-bottom: 1px solid var(--border-color);
            padding-bottom: 10px;
            display: flex;
            align-items: center;
        }

        .chart-container h3 i {
            margin-right: 8px;
        }

        .info-container {
            display: flex;
            flex-direction: column;
            gap: 20px;
        }

        .trade-info, .notifications {
            background-color: var(--medium-bg);
            border-radius: 5px;
            padding: 20px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            border: 1px solid var(--border-color);
        }

        .trade-info {
            flex: 0 0 auto;
        }

        .trade-info h3, .notifications h3 {
            margin-top: 0;
            color: var(--secondary-color);
            border-bottom: 1px solid var(--border-color);
            padding-bottom: 10px;
            display: flex;
            align-items: center;
        }

        .trade-info h3 i, .notifications h3 i {
            margin-right: 8px;
        }

        .notifications {
            flex: 1 1 auto;
            max-height: 300px;
            overflow-y: auto;
        }

        .notification {
            padding: 10px;
            margin-bottom: 10px;
            border-radius: 4px;
            border-left: 4px solid transparent;
        }

        .notification.info {
            background-color: rgba(14, 165, 233, 0.2);
            border-left-color: var(--secondary-color);
        }

        .notification.success {
            background-color: rgba(16, 185, 129, 0.2);
            border-left-color: var(--success-color);
        }

        .notification.error {
            background-color: rgba(239, 68, 68, 0.2);
            border-left-color: var(--danger-color);
        }

        .stats {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
            margin: 20px 0;
            background: rgba(31, 31, 45, 0.8);
            border-radius: 10px;
            padding: 15px;
            border: 1px solid rgba(66, 134, 244, 0.1);
        }

        .stat-card {
            background-color: var(--light-bg);
            border-radius: 5px;
            padding: 15px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
            text-align: center;
            border: 1px solid var(--border-color);
        }

        .stat-card div:first-child {
            color: var(--text-medium);
            font-size: 0.9em;
        }

        .stat-value {
            font-size: 24px;
            font-weight: bold;
            margin: 10px 0;
        }

        .win {
            color: var(--success-color);
        }

        .loss {
            color: var(--danger-color);
        }

        canvas {
            width: 100%;
            height: 300px;
            margin-top: 15px;
        }

        .pie-chart-container {
            display: flex;
            justify-content: space-around;
            margin-top: 20px;
            flex-wrap: wrap;
        }

        #winLossChart {
            width: 200px;
            height: 200px;
        }

        .chart-wrapper {
            flex: 0 0 auto;
        }

        .mode-description {
            background-color: var(--light-bg);
            border-radius: 5px;
            padding: 15px;
            margin-bottom: 20px;
            border: 1px solid var(--border-color);
        }

        .mode-description h3 {
            margin-top: 0;
            color: var(--secondary-color);
            display: flex;
            align-items: center;
        }

        .mode-description h3 i {
            margin-right: 8px;
        }

        .mode-description p {
            margin: 10px 0 0 0;
            color: var(--text-medium);
        }

        .trade-history {
            margin-top: 20px;
            background-color: var(--medium-bg);
            border-radius: 5px;
            padding: 20px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            border: 1px solid var(--border-color);
            max-height: 300px;
            overflow-y: auto;
        }

        .history-list {
            max-height: 250px;
            overflow-y: auto;
        }

        .broker-integration {
            background-color: var(--light-bg);
            border-radius: 5px;
            padding: 15px;
            margin-top: 20px;
            border: 1px solid var(--border-color);
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .broker-integration p {
            margin: 0;
            color: var(--text-medium);
        }

        .broker-btn {
            background-color: var(--warning-color);
            color: white;
            padding: 8px 15px;
            border-radius: 4px;
            cursor: pointer;
            font-weight: bold;
            transition: all 0.3s;
            margin-right: 5px;
            margin-bottom: 5px;
        }

        .broker-btn:hover {
            background-color: #d97706;
            transform: translateY(-2px);
        }

        .copy-btn {
            background-color: var(--primary-color);
            color: white;
            padding: 5px 10px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 0.9em;
            margin-top: 10px;
            display: inline-block;
            transition: all 0.3s;
        }

        .copy-btn:hover {
            background-color: #0284c7;
            transform: translateY(-2px);
        }

        .floating-container {
            position: fixed;
            z-index: 9999;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3);
            transition: all 0.3s;
        }

        .minimize-btn {
            position: absolute;
            top: 10px;
            right: 10px;
            background: none;
            border: none;
            color: var(--text-medium);
            cursor: pointer;
            padding: 5px;
            z-index: 10;
            transition: all 0.3s;
        }

        .minimize-btn:hover {
            color: var(--primary-color);
            transform: scale(1.1);
        }

        .minimized {
            width: 60px !important;
            height: 60px !important;
            overflow: hidden;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
        }

        .minimized * {
            display: none;
        }

        .minimized::after {
            content: '\f021';
            font-family: 'Font Awesome 5 Free';
            font-weight: 900;
            font-size: 24px;
            color: var(--primary-color);
        }

        footer {
            text-align: center;
            margin-top: 30px;
            padding: 15px;
            color: var(--text-dark);
            font-size: 0.9em;
            border-top: 1px solid var(--border-color);
        }

        @media (max-width: 768px) {
            .content {
                grid-template-columns: 1fr;
            }

            .settings, .trading-modes {
                flex-direction: column;
            }

            .balance-row {
                flex-direction: column;
                align-items: flex-start;
            }

            .balance {
                margin-bottom: 10px;
            }
        }
    </style>
</head>
<body>
    <header>
        <h1><i class="fas fa-robot"></i> Binary Options Trading Bot</h1>
    </header>

    <div class="container">
        <div class="trading-modes">
            <div class="mode-btn active" id="mode-quantum">
                <i class="fas fa-atom mode-icon"></i>
                <h3>QUANTUM EDGE</h3>
                <p>Advanced adaptive system with 70% - 85% win rate</p>
            </div>

            <div class="mode-btn" id="mode-ai">
                <i class="fas fa-rocket mode-icon"></i>
                <h3>NEURAL PULSE</h3>
                <p>AI-powered precision with 80% - 90% win rate</p>
            </div>
        </div>

        <div class="mode-description" id="current-mode-description">
            <h3><i class="fas fa-atom"></i> QUANTUM EDGE</h3>
            <p>A high winrate strategy with advanced money management system capable of generating consistent profit. This revolutionary technology delivers exceptional 70% - 85% win rates through adaptive position sizing and sophisticated risk control.</p>
        </div>

        <div class="control-panel">
            <div class="balance-row">
                <div class="balance" id="balance">Balance: $1000.00</div>
                <div class="balance-change" id="balance-change">+0.00%</div>
            </div>

            <div class="settings">
                <div class="setting-group">
                    <label for="riskPerTrade"><i class="fas fa-shield-alt"></i> Risk per Trade (%)</label>
                    <input type="range" id="riskPerTrade" min="0.5" max="5" step="0.1" value="2">
                    <div style="display: flex; justify-content: space-between;">
                        <span>0.5%</span>
                        <span id="riskValue">2%</span>
                        <span>5%</span>
                    </div>
                </div>

                <div class="setting-group" id="strategy-container">
                    <label for="strategy"><i class="fas fa-cogs"></i> Money Management Strategy</label>
                    <select id="strategy">
                        <option value="kelly">Kelly Criterion</option>
                        <option value="fixed">Fixed Percentage</option>
                        <option value="anti-martingale">Anti-Martingale</option>
                    </select>
                </div>

                <div class="setting-group" id="expiry-container">
                    <label for="expiryTime"><i class="fas fa-clock"></i> Expiration Time</label>
                    <select id="expiryTime">
                        <option value="30">30 seconds</option>
                        <option value="60">1 minute</option>
                        <option value="120">2 minutes</option>
                        <option value="300">5 minutes</option>
                        <option value="900">15 minutes</option>
                        <option value="1800">30 minutes</option>
                    </select>
                </div>

                <div class="setting-group">
                    <label for="autoInterval"><i class="fas fa-sync-alt"></i> Auto Trading Interval (s)</label>
                    <input type="range" id="autoInterval" min="5" max="60" step="1" value="10">
                    <div style="display: flex; justify-content: space-between;">
                        <span>5s</span>
                        <span id="intervalValue">10s</span>
                        <span>60s</span>
                    </div>
                </div>

                <div class="setting-group">
                    <label for="profitTarget"><i class="fas fa-bullseye"></i> Profit Target ($)</label>
                    <input type="number" id="profitTarget" min="0" step="0.01" value="100" placeholder="Enter profit target">
                    <div style="margin-top: 5px; font-size: 0.9em; color: var(--text-dark);">
                        <span>Set to 0 for unlimited trading</span>
                    </div>
                </div>
            </div>

            <div class="buttons">
                <button id="placeTrade" class="primary-btn"><i class="fas fa-play-circle"></i> Place Trade</button>
                <button id="autoTrading" class="secondary-btn"><i class="fas fa-robot"></i> Start Auto Trading</button>
            </div>
        </div>

        <div class="stats">
            <div class="stat-card">
                <div><i class="fas fa-percentage"></i> Win Rate</div>
                <div class="stat-value" id="winRate">0%</div>
            </div>
            <div class="stat-card">
                <div><i class="fas fa-chart-bar"></i> Profit/Loss</div>
                <div class="stat-value" id="profitLoss">0%</div>
            </div>
            <div class="stat-card">
                <div><i class="fas fa-exchange-alt"></i> Total Trades</div>
                <div class="stat-value" id="totalTrades">0</div>
            </div>
            <div class="stat-card">
                <div><i class="fas fa-trophy"></i> Consecutive Wins</div>
                <div class="stat-value win" id="consecutiveWins">0</div>
            </div>
        </div>

        <div class="trade-info">
            <h3><i class="fas fa-exchange-alt"></i> Current Trade</h3>
            <div id="tradeInfo">No active trade</div>

            <div class="result-buttons" style="display: none; margin-top: 15px;">
                <button id="winButton" class="primary-btn"><i class="fas fa-check-circle"></i> Win</button>
                <button id="loseButton" class="secondary-btn"><i class="fas fa-times-circle"></i> Lose</button>
            </div>
        </div>

        <div class="trade-history">
            <h3><i class="fas fa-history"></i> Trade History</h3>
            <div id="notificationsList" class="history-list"></div>
        </div>

        <div class="broker-integration">
            <p><i class="fas fa-link"></i> Connect to your broker platform</p>
            <div>
                <button class="broker-btn" id="connectBroker">Connect to Pocket Option</button>
                <button class="broker-btn" id="floatingMode"><i class="fas fa-external-link-alt"></i> Floating Mode</button>
            </div>
        </div>
    </div>

    <footer>
        <p>Binary Options Trading Bot v1.0</p>
    </footer>

    <script>
        // Initialize variables
        let balance = 1000;
        let initialBalance = 1000;
        let riskPerTrade = 0.02;
        let strategy = 'kelly';
        let autoInterval = 10;
        let profitTarget = 100; // Default profit target
        let isAutoTrading = false;
        let activeTrade = false;
        let tradeTimeout;
        let autoTradeInterval;
        let currentMode = 'quantum'; // Default mode: quantum edge
        let payoutPercentage = 85; // Default payout percentage
        let currentCurrencyPair = 'EUR/USD';

        // Trading statistics
        let totalTrades = 0;
        let winningTrades = 0;
        let losingTrades = 0;
        let consecutiveWins = 0;
        let consecutiveLosses = 0;

        // Trade history
        let tradeHistory = [];

        // DOM elements
        const balanceElement = document.getElementById('balance');
        const balanceChangeElement = document.getElementById('balance-change');
        const riskSlider = document.getElementById('riskPerTrade');
        const riskValue = document.getElementById('riskValue');
        const strategySelect = document.getElementById('strategy');
        const expirySelect = document.getElementById('expiryTime');
        const intervalSlider = document.getElementById('autoInterval');
        const intervalValue = document.getElementById('intervalValue');
        const profitTargetInput = document.getElementById('profitTarget');
        const placeTradeBtn = document.getElementById('placeTrade');
        const autoTradingBtn = document.getElementById('autoTrading');
        const tradeInfoElement = document.getElementById('tradeInfo');
        const notificationsElement = document.getElementById('notificationsList');
        const winRateElement = document.getElementById('winRate');
        const profitLossElement = document.getElementById('profitLoss');
        const totalTradesElement = document.getElementById('totalTrades');
        const consecutiveWinsElement = document.getElementById('consecutiveWins');
        const winButton = document.getElementById('winButton');
        const loseButton = document.getElementById('loseButton');
        const modeDescription = document.getElementById('current-mode-description');
        const connectBrokerBtn = document.getElementById('connectBroker');
        const floatingModeBtn = document.getElementById('floatingMode');

        // Mode buttons
        const quantumModeBtn = document.getElementById('mode-quantum');
        const neuralModeBtn = document.getElementById('mode-ai');

        // Add minimize button to container
        const container = document.querySelector('.container');
        const minimizeBtn = document.createElement('button');
        minimizeBtn.className = 'minimize-btn';
        minimizeBtn.innerHTML = '<i class="fas fa-minus"></i>';
        container.prepend(minimizeBtn);

        // Event listeners
        riskSlider.addEventListener('input', () => {
            riskPerTrade = parseFloat(riskSlider.value) / 100;
            riskValue.textContent = `${riskSlider.value}%`;
        });

        strategySelect.addEventListener('change', () => {
            strategy = strategySelect.value;
        });

        intervalSlider.addEventListener('input', () => {
            autoInterval = parseInt(intervalSlider.value);
            intervalValue.textContent = `${autoInterval}s`;
        });

        profitTargetInput.addEventListener('change', () => {
            profitTarget = parseFloat(profitTargetInput.value) || 0;
            addNotification(`Profit target updated to $${profitTarget.toFixed(2)}`, 'info');
        });

        placeTradeBtn.addEventListener('click', placeTrade);
        autoTradingBtn.addEventListener('click', toggleAutoTrading);
        winButton.addEventListener('click', () => handleTradeResult('WIN'));
        loseButton.addEventListener('click', () => handleTradeResult('LOSS'));

        // Mode selection event listeners
        quantumModeBtn.addEventListener('click', () => setMode('quantum'));
        neuralModeBtn.addEventListener('click', () => setMode('neural'));

        // Broker integration event listeners
        connectBrokerBtn.addEventListener('click', connectToPocketOption);
        floatingModeBtn.addEventListener('click', toggleFloatingMode);
        minimizeBtn.addEventListener('click', toggleMinimize);

        // Double click on minimized container to maximize
        container.addEventListener('click', function(e) {
            if (container.classList.contains('minimized')) {
                toggleMinimize();
            }
        });

        // Functions
        function setMode(mode) {
            currentMode = mode;

            // Update UI
            quantumModeBtn.classList.remove('active');
            neuralModeBtn.classList.remove('active');

            // Update mode description
            if (mode === 'quantum') {
                quantumModeBtn.classList.add('active');
                modeDescription.innerHTML = `
                    <h3><i class="fas fa-atom"></i> QUANTUM EDGE</h3>
                    <p>A high winrate strategy with advanced money management system capable of generating consistent profit. This revolutionary technology delivers an exceptional 75% win rate through adaptive market analysis.</p>
                `;

                // Show strategy selection for quantum mode
                document.getElementById('strategy-container').style.display = 'block';

                // Set default expiry times for this mode
                updateExpiryOptions([60, 120, 180, 300]);
            }
            else if (mode === 'neural') {
                neuralModeBtn.classList.add('active');
                modeDescription.innerHTML = `
                    <h3><i class="fas fa-rocket"></i> NEURAL PULSE</h3>
                    <p>Our most advanced system, Neural Pulse™, harnesses the power of deep learning AI to analyze market patterns with unprecedented accuracy. With a stunning 90% win rate, this elite system represents the pinnacle of binary options trading technology.</p>
                `;

                // Hide strategy selection for neural mode (uses AI)
                document.getElementById('strategy-container').style.display = 'none';

                // Set longer expiry times for AI mode
                updateExpiryOptions([300, 900, 1800]);
            }

            addNotification(`Switched to ${mode.toUpperCase()} mode`, 'info');
        }

        function updateExpiryOptions(times) {
            // Clear existing options
            expirySelect.innerHTML = '';

            // Add new options
            times.forEach(time => {
                const option = document.createElement('option');
                option.value = time;

                if (time < 60) {
                    option.textContent = `${time} seconds`;
                } else if (time < 3600) {
                    option.textContent = `${time / 60} minute${time === 60 ? '' : 's'}`;
                } else {
                    option.textContent = `${time / 3600} hour${time === 3600 ? '' : 's'}`;
                }

                expirySelect.appendChild(option);
            });
        }

        function placeTrade() {
            if (activeTrade) {
                addNotification('Cannot place new trade, active trade in progress', 'info');
                return;
            }

            // Calculate trade amount based on strategy and mode
            const tradeAmount = calculatePositionSize();

            // Generate direction (buy/sell)
            const direction = determineTradeDirection();

            // Get expiry time from select
            const expiry = parseInt(expirySelect.value);

            // Set active trade
            activeTrade = true;
            const startTime = new Date();
            const endTime = new Date(startTime.getTime() + expiry * 1000);

            // Store current trade details
            currentTrade = {
                direction,
                amount: tradeAmount,
                expiry,
                startTime,
                endTime
            };

            // Update trade info
            updateTradeInfo(direction, tradeAmount, expiry, startTime, endTime);

            // Show result buttons
            document.querySelector('.result-buttons').style.display = 'block';

            // Add notification
            addNotification(`New trade: ${currentCurrencyPair} - ${direction} for $${tradeAmount.toFixed(2)} with ${formatTime(expiry)} expiry`, 'info');

            // If auto trading, set timeout for automatic result
            if (isAutoTrading) {
                tradeTimeout = setTimeout(() => {
                    const winRate = getWinRateForCurrentMode();
                    const outcome = Math.random() < winRate;
                    handleTradeResult(outcome ? 'WIN' : 'LOSS');
                }, expiry * 1000);
            }
        }

        function formatTime(seconds) {
            if (seconds < 60) {
                return `${seconds}s`;
            } else if (seconds < 3600) {
                return `${seconds / 60}m`;
            } else {
                return `${seconds / 3600}h`;
            }
        }

        function getWinRateForCurrentMode() {
            switch (currentMode) {
                case 'quantum': return 0.65;  // 65% win rate
                case 'neural': return 0.90;   // 90% win rate
                default: return 0.55;
            }
        }

        function determineTradeDirection() {
            // In a real system, this would use actual analysis
            // For now, we'll use random with slight bias based on mode

            if (currentMode === 'neural') {
                // Neural mode has higher accuracy
                return Math.random() < 0.55 ? 'BUY' : 'SELL';
            } else {
                return Math.random() < 0.5 ? 'BUY' : 'SELL';
            }
        }

        function calculatePositionSize() {
            let positionSize;

            if (currentMode === 'neural') {
                // AI mode - more aggressive due to higher win rate
                positionSize = balance * (riskPerTrade * 1.5);
            }
            else {
                // Quantum mode - uses selected strategy
                if (strategy === 'kelly') {
                    // Modified Kelly Criterion
                    const winRate = getWinRateForCurrentMode();
                    const payoutRatio = payoutPercentage / 100;

                    const p = winRate;
                    const q = 1 - p;
                    const b = payoutRatio;

                    // Kelly percentage
                    const kellyPercentage = (p * (1 + b) - 1) / b;

                    // Apply safety factor (1/4 Kelly)
                    const safeKelly = kellyPercentage * 0.25;

                    // Cap at maximum risk per trade
                    positionSize = Math.min(safeKelly, riskPerTrade) * balance;
                }
                else if (strategy === 'anti-martingale') {
                    // Anti-martingale approach
                    const baseSize = balance * riskPerTrade;

                    if (consecutiveWins > 0) {
                        // Increase by 50% for each consecutive win, up to 3 wins
                        const multiplier = Math.min(1 + (0.5 * consecutiveWins), 2.5);
                        positionSize = baseSize * multiplier;
                    }
                    else if (consecutiveLosses > 0) {
                        // Decrease by 30% for each consecutive loss
                        const multiplier = Math.max(1 - (0.3 * consecutiveLosses), 0.4);
                        positionSize = baseSize * multiplier;
                    }
                    else {
                        positionSize = baseSize;
                    }
                }
                else {
                    // Fixed percentage
                    positionSize = balance * riskPerTrade;
                }
            }

            return Math.round(positionSize * 100) / 100; // Round to 2 decimal places
        }

        function handleTradeResult(result) {
            if (!activeTrade) return;

            // Clear any pending timeout
            if (tradeTimeout) {
                clearTimeout(tradeTimeout);
                tradeTimeout = null;
            }

            const tradeAmount = currentTrade.amount;
            const direction = currentTrade.direction;

            // Update balance
            if (result === 'WIN') {
                const profit = tradeAmount * (payoutPercentage / 100);
                balance += profit;
                winningTrades++;
                consecutiveWins++;
                consecutiveLosses = 0;
                addNotification(`Trade ${result}: ${currentCurrencyPair} - ${direction} +$${profit.toFixed(2)} | Balance: $${balance.toFixed(2)}`, 'success');
            } else {
                balance -= tradeAmount;
                losingTrades++;
                consecutiveLosses++;
                consecutiveWins = 0;
                addNotification(`Trade ${result}: ${currentCurrencyPair} - ${direction} -$${tradeAmount.toFixed(2)} | Balance: $${balance.toFixed(2)}`, 'error');
            }

            totalTrades++;

            // Update trade history
            tradeHistory.push({
                direction,
                amount: tradeAmount,
                result,
                balance
            });

            // Reset active trade
            activeTrade = false;
            currentTrade = null;

            // Hide result buttons
            document.querySelector('.result-buttons').style.display = 'none';

            // Update UI
            updateBalance();
            updateTradeInfo();
            updateStatistics();

            // Check if profit target has been reached
            if (profitTarget > 0) {
                const currentProfit = balance - initialBalance;
                if (currentProfit >= profitTarget) {
                    // Stop auto trading
                    if (isAutoTrading) {
                        toggleAutoTrading();
                    }

                    // Show congratulations popup
                    showProfitTargetReached(currentProfit);
                    return; // Don't continue trading
                }
            }

            // Continue auto trading if enabled
            if (isAutoTrading) {
                setTimeout(() => {
                    if (!activeTrade) {
                        placeTrade();
                    }
                }, autoInterval * 1000);
            }
        }

        function toggleAutoTrading() {
            isAutoTrading = !isAutoTrading;

            if (isAutoTrading) {
                autoTradingBtn.innerHTML = '<i class="fas fa-stop-circle"></i> Stop Auto Trading';
                autoTradingBtn.classList.remove('secondary-btn');
                autoTradingBtn.classList.add('primary-btn');

                // Start first trade immediately
                if (!activeTrade) {
                    placeTrade();
                }

                addNotification(`Auto trading started with ${autoInterval}s interval`, 'info');
            } else {
                autoTradingBtn.innerHTML = '<i class="fas fa-robot"></i> Start Auto Trading';
                autoTradingBtn.classList.remove('primary-btn');
                autoTradingBtn.classList.add('secondary-btn');

                // Clear auto trading interval
                if (tradeTimeout) {
                    clearTimeout(tradeTimeout);
                    tradeTimeout = null;
                }

                addNotification('Auto trading stopped', 'info');
            }
        }

        function updateBalance() {
            balanceElement.textContent = `Balance: $${balance.toFixed(2)}`;

            // Update balance change percentage
            const changePercentage = ((balance - initialBalance) / initialBalance) * 100;
            const currentProfit = balance - initialBalance;

            // Show profit target progress if target is set
            if (profitTarget > 0) {
                const targetProgress = (currentProfit / profitTarget) * 100;
                const progressText = targetProgress >= 100 ?
                    `🎯 TARGET REACHED! +${changePercentage.toFixed(2)}%` :
                    `${changePercentage >= 0 ? '+' : ''}${changePercentage.toFixed(2)}% (${targetProgress.toFixed(1)}% to target)`;
                balanceChangeElement.textContent = progressText;
            } else {
                balanceChangeElement.textContent = `${changePercentage >= 0 ? '+' : ''}${changePercentage.toFixed(2)}%`;
            }

            if (changePercentage >= 0) {
                balanceChangeElement.classList.remove('negative');
            } else {
                balanceChangeElement.classList.add('negative');
            }
        }

        function updateTradeInfo(direction, amount, expiry, startTime, endTime) {
            if (!activeTrade) {
                tradeInfoElement.textContent = 'No active trade';
                return;
            }

            // Calculate remaining time
            const now = new Date();
            const remaining = Math.max(0, Math.round((endTime - now) / 1000));

            // Calculate potential profit
            const potentialProfit = amount * (payoutPercentage / 100);

            tradeInfoElement.innerHTML = `
                <p><strong>Asset:</strong> ${currentCurrencyPair}</p>
                <p><strong>Direction:</strong> <span style="color: ${direction === 'BUY' ? '#10b981' : '#ef4444'}">${direction}</span></p>
                <p><strong>Amount:</strong> $${amount.toFixed(2)}</p>
                <p><strong>Potential Profit:</strong> $${potentialProfit.toFixed(2)}</p>
                <p><strong>Time Remaining:</strong> ${formatTime(remaining)}</p>
                <button class="copy-btn" id="copyTradeBtn">
                    <i class="fas fa-copy"></i> Copy Trade to Clipboard
                </button>
            `;

            // Add event listener to copy button
            document.getElementById('copyTradeBtn').addEventListener('click', function() {
                const tradeDetails = `
Trade Details for Pocket Option:
Asset: ${currentCurrencyPair}
Direction: ${direction}
Amount: $${amount.toFixed(2)}
Expiration: ${formatTime(expiry)}
Potential Profit: $${potentialProfit.toFixed(2)}
                `.trim();

                navigator.clipboard.writeText(tradeDetails)
                    .then(() => {
                        this.innerHTML = '<i class="fas fa-check"></i> Copied!';
                        setTimeout(() => {
                            this.innerHTML = '<i class="fas fa-copy"></i> Copy Trade to Clipboard';
                        }, 2000);
                    })
                    .catch(err => {
                        console.error('Failed to copy: ', err);
                        alert('Trade details:\n\n' + tradeDetails);
                    });
            });

            // Update remaining time every second
            if (remaining > 0) {
                setTimeout(() => {
                    if (activeTrade) {
                        updateTradeInfo(direction, amount, expiry, startTime, endTime);
                    }
                }, 1000);
            }
        }

        function addNotification(message, type) {
            const notification = document.createElement('div');
            notification.className = `notification ${type}`;

            const timestamp = new Date().toLocaleTimeString();
            notification.textContent = `[${timestamp}] ${message}`;

            notificationsElement.prepend(notification);

            // Limit to 10 notifications
            if (notificationsElement.children.length > 10) {
                notificationsElement.removeChild(notificationsElement.lastChild);
            }
        }

        function updateStatistics() {
            // Calculate win rate
            const winRate = totalTrades > 0 ? (winningTrades / totalTrades) * 100 : 0;

            // Calculate profit/loss percentage
            const profitLossPercentage = ((balance - initialBalance) / initialBalance) * 100;

            // Update UI
            winRateElement.textContent = `${winRate.toFixed(1)}%`;
            profitLossElement.textContent = `${profitLossPercentage.toFixed(1)}%`;
            profitLossElement.className = 'stat-value ' + (profitLossPercentage >= 0 ? 'win' : 'loss');
            totalTradesElement.textContent = totalTrades;
            consecutiveWinsElement.textContent = consecutiveWins;
        }

        /**
         * Show a congratulations popup when profit target is reached
         */
        function showProfitTargetReached(profit) {
            // Create overlay
            const overlay = document.createElement('div');
            overlay.style.position = 'fixed';
            overlay.style.top = '0';
            overlay.style.left = '0';
            overlay.style.width = '100%';
            overlay.style.height = '100%';
            overlay.style.backgroundColor = 'rgba(0, 0, 0, 0.8)';
            overlay.style.zIndex = '10000';
            overlay.style.display = 'flex';
            overlay.style.justifyContent = 'center';
            overlay.style.alignItems = 'center';
            overlay.style.animation = 'fadeIn 0.3s ease-in';

            // Create popup content
            const popup = document.createElement('div');
            popup.style.backgroundColor = 'var(--medium-bg)';
            popup.style.borderRadius = '20px';
            popup.style.padding = '40px';
            popup.style.textAlign = 'center';
            popup.style.maxWidth = '500px';
            popup.style.width = '90%';
            popup.style.border = '3px solid var(--success-color)';
            popup.style.boxShadow = '0 20px 60px rgba(0, 0, 0, 0.5), 0 0 30px rgba(16, 185, 129, 0.3)';
            popup.style.animation = 'popIn 0.5s ease-out';

            // Create congratulations content
            const congratsMessages = [
                "🎉 Outstanding Achievement! 🎉",
                "🚀 Mission Accomplished! 🚀",
                "💎 Profit Target Crushed! 💎",
                "🏆 Trading Champion! 🏆",
                "⭐ Stellar Performance! ⭐"
            ];

            const randomMessage = congratsMessages[Math.floor(Math.random() * congratsMessages.length)];

            const winRate = totalTrades > 0 ? (winningTrades / totalTrades) * 100 : 0;
            const profitPercentage = ((profit / initialBalance) * 100);

            popup.innerHTML = `
                <div style="margin-bottom: 20px;">
                    <h1 style="color: var(--success-color); margin: 0; font-size: 2.5em; text-shadow: 0 0 20px rgba(16, 185, 129, 0.5);">
                        ${randomMessage}
                    </h1>
                </div>

                <div style="margin-bottom: 30px;">
                    <p style="color: var(--text-light); font-size: 1.3em; margin: 10px 0;">
                        You've successfully reached your profit target!
                    </p>
                    <p style="color: var(--text-medium); font-size: 1.1em; margin: 5px 0;">
                        The KOS Trading Bot has delivered exceptional results!
                    </p>
                </div>

                <div style="background: linear-gradient(135deg, var(--light-bg) 0%, var(--medium-bg) 100%);
                           border-radius: 15px; padding: 25px; margin: 20px 0;
                           border: 1px solid var(--border-color);">
                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; text-align: center;">
                        <div>
                            <div style="color: var(--text-dark); font-size: 0.9em;">Initial Balance</div>
                            <div style="color: var(--text-light); font-size: 1.4em; font-weight: bold;">$${initialBalance.toFixed(2)}</div>
                        </div>
                        <div>
                            <div style="color: var(--text-dark); font-size: 0.9em;">Final Balance</div>
                            <div style="color: var(--success-color); font-size: 1.4em; font-weight: bold;">$${balance.toFixed(2)}</div>
                        </div>
                        <div>
                            <div style="color: var(--text-dark); font-size: 0.9em;">Total Profit</div>
                            <div style="color: var(--success-color); font-size: 1.8em; font-weight: bold; text-shadow: 0 0 10px rgba(16, 185, 129, 0.3);">$${profit.toFixed(2)}</div>
                        </div>
                        <div>
                            <div style="color: var(--text-dark); font-size: 0.9em;">Profit %</div>
                            <div style="color: var(--success-color); font-size: 1.8em; font-weight: bold; text-shadow: 0 0 10px rgba(16, 185, 129, 0.3);">+${profitPercentage.toFixed(1)}%</div>
                        </div>
                        <div>
                            <div style="color: var(--text-dark); font-size: 0.9em;">Win Rate</div>
                            <div style="color: var(--text-light); font-size: 1.4em; font-weight: bold;">${winRate.toFixed(1)}%</div>
                        </div>
                        <div>
                            <div style="color: var(--text-dark); font-size: 0.9em;">Total Trades</div>
                            <div style="color: var(--text-light); font-size: 1.4em; font-weight: bold;">${totalTrades}</div>
                        </div>
                    </div>
                </div>

                <div style="margin-top: 30px;">
                    <button id="continueTrading" style="background: var(--primary-color); color: white;
                           border: none; padding: 12px 25px; border-radius: 8px; font-size: 1.1em;
                           font-weight: bold; cursor: pointer; margin-right: 15px;
                           transition: all 0.3s; box-shadow: 0 4px 15px rgba(66, 134, 244, 0.3);">
                        <i class="fas fa-play"></i> Continue Trading
                    </button>
                    <button id="resetBot" style="background: var(--warning-color); color: white;
                           border: none; padding: 12px 25px; border-radius: 8px; font-size: 1.1em;
                           font-weight: bold; cursor: pointer; transition: all 0.3s;
                           box-shadow: 0 4px 15px rgba(245, 158, 11, 0.3);">
                        <i class="fas fa-refresh"></i> Reset & Start Over
                    </button>
                </div>
            `;

            // Add CSS animations
            const style = document.createElement('style');
            style.textContent = `
                @keyframes fadeIn {
                    from { opacity: 0; }
                    to { opacity: 1; }
                }
                @keyframes popIn {
                    from { transform: scale(0.5) rotate(-5deg); opacity: 0; }
                    to { transform: scale(1) rotate(0deg); opacity: 1; }
                }
                #continueTrading:hover {
                    background: #0284c7 !important;
                    transform: translateY(-2px);
                    box-shadow: 0 6px 20px rgba(66, 134, 244, 0.4) !important;
                }
                #resetBot:hover {
                    background: #d97706 !important;
                    transform: translateY(-2px);
                    box-shadow: 0 6px 20px rgba(245, 158, 11, 0.4) !important;
                }
            `;
            document.head.appendChild(style);

            overlay.appendChild(popup);
            document.body.appendChild(overlay);

            // Add event listeners
            document.getElementById('continueTrading').addEventListener('click', () => {
                document.body.removeChild(overlay);
                document.head.removeChild(style);
                addNotification('Trading resumed after reaching profit target', 'info');
            });

            document.getElementById('resetBot').addEventListener('click', () => {
                document.body.removeChild(overlay);
                document.head.removeChild(style);
                resetBot();
            });

            // Add notification
            addNotification(`🎯 Profit target of $${profitTarget.toFixed(2)} reached! Total profit: $${profit.toFixed(2)}`, 'success');
        }

        /**
         * Reset the bot to initial state
         */
        function resetBot() {
            balance = initialBalance;
            totalTrades = 0;
            winningTrades = 0;
            losingTrades = 0;
            consecutiveWins = 0;
            consecutiveLosses = 0;
            tradeHistory = [];

            // Stop auto trading if active
            if (isAutoTrading) {
                toggleAutoTrading();
            }

            // Reset active trade
            activeTrade = false;
            currentTrade = null;

            // Clear any timeouts
            if (tradeTimeout) {
                clearTimeout(tradeTimeout);
                tradeTimeout = null;
            }

            // Hide result buttons
            document.querySelector('.result-buttons').style.display = 'none';

            // Update UI
            updateBalance();
            updateTradeInfo();
            updateStatistics();

            addNotification('Bot reset to initial state', 'info');
        }

        // Functions for Pocket Option integration
        function connectToPocketOption() {
            // Open Pocket Option in a new tab
            window.open('https://pocketoption.com/en/cabinet/demo-quick-high-low/', '_blank');

            // Show instructions
            alert('Instructions for connecting to Pocket Option:\n\n1. Log in to your Pocket Option account in the new tab\n2. Click "Floating Mode" to make this bot interface float over Pocket Option\n3. Use the "Copy Trade" button to copy trade details\n4. Paste the details into Pocket Option to execute trades');

            addNotification('Opened Pocket Option in a new tab', 'info');
        }

        function toggleFloatingMode() {
            container.classList.toggle('floating-container');

            if (container.classList.contains('floating-container')) {
                // Make draggable
                makeDraggable(container);

                // Position in top right corner
                container.style.top = '20px';
                container.style.right = '20px';
                container.style.left = 'auto';

                floatingModeBtn.innerHTML = '<i class="fas fa-compress-alt"></i> Normal Mode';
                addNotification('Switched to floating mode', 'info');
            } else {
                // Remove draggable
                removeDraggable(container);

                // Reset position
                container.style.position = '';
                container.style.top = '';
                container.style.right = '';
                container.style.left = '';

                floatingModeBtn.innerHTML = '<i class="fas fa-external-link-alt"></i> Floating Mode';
                addNotification('Switched to normal mode', 'info');
            }
        }

        function toggleMinimize() {
            container.classList.toggle('minimized');

            if (container.classList.contains('minimized')) {
                minimizeBtn.innerHTML = '<i class="fas fa-expand"></i>';
                addNotification('Interface minimized', 'info');
            } else {
                minimizeBtn.innerHTML = '<i class="fas fa-minus"></i>';
                addNotification('Interface maximized', 'info');
            }
        }

        function makeDraggable(element) {
            let isDragging = false;
            let offsetX, offsetY;

            element.addEventListener('mousedown', startDrag);
            document.addEventListener('mousemove', drag);
            document.addEventListener('mouseup', stopDrag);

            function startDrag(e) {
                // Don't start drag if clicking on a button or input
                if (e.target.tagName === 'BUTTON' || e.target.tagName === 'INPUT' || e.target.tagName === 'SELECT') {
                    return;
                }

                isDragging = true;
                offsetX = e.clientX - element.getBoundingClientRect().left;
                offsetY = e.clientY - element.getBoundingClientRect().top;
                element.style.cursor = 'grabbing';
            }

            function drag(e) {
                if (!isDragging) return;

                element.style.left = (e.clientX - offsetX) + 'px';
                element.style.top = (e.clientY - offsetY) + 'px';
                element.style.right = 'auto';
            }

            function stopDrag() {
                isDragging = false;
                element.style.cursor = '';
            }

            // Store the event listeners for later removal
            element._dragListeners = {
                startDrag,
                drag,
                stopDrag
            };
        }

        function removeDraggable(element) {
            if (!element._dragListeners) return;

            element.removeEventListener('mousedown', element._dragListeners.startDrag);
            document.removeEventListener('mousemove', element._dragListeners.drag);
            document.removeEventListener('mouseup', element._dragListeners.stopDrag);

            element._dragListeners = null;
        }

        // Initialize UI
        updateBalance();
        updateStatistics();
        setMode('quantum'); // Set default mode

        // Add initial notification
        addNotification('Binary Options Trading Bot initialized', 'info');
    </script>
</body>
</html>
