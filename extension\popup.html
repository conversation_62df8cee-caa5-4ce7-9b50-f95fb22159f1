<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>KOS Trading Bot</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="popup.css">
</head>
<body>
    <header>
        <h1><i class="fas fa-robot"></i> KOS Trading Bot</h1>
        <div class="subtitle">Professional trading automation for Pocket Option</div>
    </header>

    <div class="container">
        <div class="connection-panel">
            <div class="status-indicator" id="connection-status">
                <i class="fas fa-plug"></i> <span>Not connected to Pocket Option</span>
            </div>
            <button id="connectBtn" class="connect-btn">Connect to Pocket Option</button>
        </div>

        <div class="mode-selection">
            <h2>Trading Modes</h2>



            <div class="mode-card" id="mode-quantum">
                <div class="mode-header">
                    <i class="fas fa-atom"></i>
                    <h3>QUANTUM EDGE</h3>
                </div>
                <div class="mode-content">
                    <div class="win-rate">70% - 85% Win Rate</div>
                    <p>A high winrate strategy with advanced money management system capable of generating consistent profit. Features adaptive position sizing and sophisticated risk control.</p>
                </div>
            </div>



            <div class="mode-card" id="mode-neural">
                <div class="mode-header">
                    <i class="fas fa-rocket"></i>
                    <h3>NEURAL PULSE</h3>
                </div>
                <div class="mode-content">
                    <div class="win-rate">80% - 90% Win Rate</div>
                    <p>Our most advanced system, Neural Pulse™ harnesses the power of deep learning AI to analyze market patterns with unprecedented accuracy.</p>
                </div>
            </div>
        </div>

        <div class="info-panel">
            <div class="info-item">
                <i class="fas fa-info-circle"></i>
                <p>Connect to Pocket Option to activate the floating interface and start trading.</p>
            </div>
            <div class="debug-container">
                <button id="debugBtn" class="debug-btn"><i class="fas fa-desktop"></i> Open Trading Interface</button>
            </div>
        </div>
    </div>

    <footer>
        <p>KOS Trading Bot v1.0</p>
    </footer>

    <script src="popup.js"></script>
</body>
</html>
